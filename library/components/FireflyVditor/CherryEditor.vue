<template>
  <div class="firefly-cherry-editor-container">
    <div
      class="firefly-cherry-editor"
      style="
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: nowrap;
      "
    >
      <div v-if="props.readonly">
        <!-- 传送插槽到文档预览顶部 -->
        <teleport
          v-if="props.readonly && previewHeaderEl"
          :to="previewHeaderEl"
        >
          <div class="content-header-wrapper">
            <slot name="content-header" />
          </div>
        </teleport>
      </div>

      <!-- <div class="editor-main"> -->
      <div
        ref="cherryContainer"
        :id="editorId"
        :style="editorContainerStyle"
        @click="
          (e) => {
            emit('container-click', e)
          }
        "
      ></div>
      <!-- 右侧大纲 -->
      <!-- </div> -->
      <div style="height: calc(100vh - 185px)" v-if="props.readonly">
        <div ref="outlineEl" class="cherry-markdown-outline"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    onMounted,
    onUnmounted,
    computed,
    nextTick,
    shallowRef,
    markRaw,
    watch,
  } from 'vue'
  import Cherry from 'cherry-markdown'
  import 'cherry-markdown/dist/cherry-markdown.css'
  import { useUserStore } from '@/store/modules/user'
  import { UploadServer } from '@/api/setting'

  import _ from 'lodash'

  const userStore = useUserStore()
  const { token } = userStore

  const props = defineProps({
    value: { type: String, default: '' },
    readonly: { type: Boolean, default: false },
    divId: { type: String, default: '' },
    width: { type: [Number, String], default: 0 },
    height: { type: [Number, String], default: 'auto' },
    options: { type: Object, default: () => ({}) },
    contentPaddingRight: { type: String, default: '0px' },
  })
  const emit = defineEmits([
    'update:value',
    'on-fullscreen',
    'on-upload-success',
    'content-on-change',
    'container-click',
  ])

  const cherryContainer = ref(null)
  const cherryInstance = shallowRef(null)
  const previewerEl = ref(null)
  const previewHeaderEl = ref(null)
  const outlineEl = ref(null)
  let observerInstance = null

  const editorId = computed(() =>
    props.divId ? props.divId : `cherry-editor-${Date.now()}`
  )

  const editorContainerStyle = computed(() => {
    const style = {}
    style.width = props.width
      ? typeof props.width === 'number'
        ? `${props.width}px`
        : props.width
      : '100%'
    style.height =
      props.height && props.height !== 'auto'
        ? typeof props.height === 'number'
          ? `${props.height}px`
          : props.height
        : '600px'
    return style
  })

  /**
   * 自定义一个自定义菜单按钮
   * 点第一次时，把选中的文字变成同时加粗和斜体
   * 保持光标选区不变，点第二次时，把加粗斜体的文字变成普通文本
   */
  let customMenuA = Cherry.createMenuHook('加粗斜体', {
    iconName: 'font',
    onClick: function (selection) {
      // 获取用户选中的文字，调用getSelection方法后，如果用户没有选中任何文字，会尝试获取光标所在位置的单词或句子
      let $selection = this.getSelection(selection) || '同时加粗斜体'

      // 如果是单选，并且选中内容的开始结束内没有加粗斜体语法，则尝试扩大选中范围
      if (!this.isSelections && !/^\s*(\*\*\*)[\s\S]+(\1)/.test($selection)) {
        this.getMoreSelection('***', '***', () => {
          // 调用codemirror的api获取当前选中的文本
          const newSelection = this.editor.editor.getSelection()
          // 判断是否已经是加粗斜体语法了
          const isBoldItalic = /^\s*(\*\*\*)[\s\S]+(\1)/.test(newSelection)
          if (isBoldItalic) {
            $selection = newSelection
          }
          return isBoldItalic
        })
      }

      // 如果选中的文本中已经有加粗语法了，则去掉加粗语法
      if (/^\s*(\*\*\*)[\s\S]+(\1)/.test($selection)) {
        // return $selection.replace(/(^)(\s*)(\*\*\*)([^\n]+)(\3)(\s*)($)/gm, '$1$4$7');  // 这个正则主要是为了支持多行，可以忽略
        return $selection.replace(/^\*\*\*/, '').replace(/\*\*\*$/, '')
      }

      /**
       * 注册缩小选区的规则
       *    注册后，插入“***TEXT***”，选中状态会变成“***【TEXT】***”
       *    如果不注册，插入后选中效果为：“【***TEXT***】”
       */
      this.registerAfterClickCb(() => {
        this.setLessSelection('***', '***')
      })
      // 增加加粗斜体语法
      // return $selection.replace(/(^)([^\n]+)($)/gm, '$1***$2***$3');  // 这个正则主要是为了支持多行，可以忽略
      return `***${$selection}***`
    },
  })

  const cherryToolbar = {
    // 定义顶部工具栏
    toolbar: [
      'undo',
      'redo',
      '|',
      // 把字体样式类按钮都放在加粗按钮下面
      {
        bold: [
          'bold',
          'italic',
          'underline',
          'strikethrough',
          'sub',
          'sup',
          'ruby',
          'bold&italic',
        ],
      },
      'color',
      'size',
      '|',
      'header',
      'list',
      'panel',
      '|',
      // 把插入类按钮都放在插入按钮下面
      {
        insert: [
          'image',
          'audio',
          'video',
          'link',
          'hr',
          'br',
          'code',
          'formula',
          'toc',
          'table',
        ],
      },
      'graph',
    ],
    // 定义顶部右侧工具栏，默认为空
    toolbarRight: ['fullScreen', 'togglePreview'],
    // 定义选中文字时弹出的“悬浮工具栏”，默认为 ['bold', 'italic', 'underline', 'strikethrough', 'sub', 'sup', 'quote', '|', 'size', 'color']
    bubble: [
      'bold',
      'italic',
      'underline',
      'strikethrough',
      'sub',
      'sup',
      'ruby',
      '|',
      'color',
      'size',
    ],
    // 定义光标出现在行首位置时出现的“提示工具栏”，默认为 ['h1', 'h2', 'h3', '|', 'checklist', 'quote', 'table', 'code']
    float: ['table', 'code', 'graph'],
    // 声明自定义按钮
    customMenu: {
      'bold&italic': customMenuA,
    },
  }

  // ==== 新增：生成大纲 ====
  const updateOutline = _.debounce(() => {
    if (!outlineEl.value || !previewerEl.value) return
    outlineEl.value.innerHTML = ''

    const headings = previewerEl.value.querySelectorAll(
      'h1, h2, h3, h4, h5, h6'
    )
    if (!headings.length) {
      outlineEl.value.innerHTML =
        '<div class="cherry-markdown-outline__empty">无大纲</div>'
      return
    }

    headings.forEach((heading) => {
      const level = parseInt(heading.tagName[1])
      const text = heading.textContent.trim()
      const id =
        heading.id || `outline-${text.replace(/\s+/g, '-').toLowerCase()}`

      if (!heading.id) heading.id = id

      const item = document.createElement('div')
      item.classList.add('cherry-markdown-outline__item')
      item.style.paddingLeft = `${(level - 1) * 16}px`
      item.innerHTML = `<a href="#${id}">${text}</a>`

      item.addEventListener('click', (e) => {
        e.preventDefault()
        heading.scrollIntoView({ behavior: 'smooth', block: 'center' })
      })

      outlineEl.value.appendChild(item)
    })
  }, 200)

  // 预览头锚点保障：在预览 DOM 被 Cherry 重绘后，确保 header 锚点存在
  const ensurePreviewHeader = _.debounce(() => {
    if (!props.readonly) return
    if (!previewerEl.value) return
    if (previewHeaderEl.value && previewHeaderEl.value.isConnected) return

    const headerDiv = document.createElement('div')
    headerDiv.className = 'firefly-cherry-editor__preview-header-anchor'
    previewerEl.value.insertBefore(headerDiv, previewerEl.value.firstChild)
    previewHeaderEl.value = headerDiv
  }, 300)

  const bindOutlineObserver = () => {
    if (!previewerEl.value) return
    observerInstance = new MutationObserver(() => {
      ensurePreviewHeader()
      updateOutline()
    })
    observerInstance.observe(previewerEl.value, {
      childList: true,
      subtree: true,
    })
  }

  /**
   * 上传文件函数
   * @param file 上传文件的文件对象
   * @param callback 回调函数，回调函数接收两个参数，第一个参数为文件上传后的url，第二个参数可选，为额外配置信息
   */
  const myFileUpload = async (file, callback) => {
    try {
      // 验证文件对象
      if (!file) {
        throw new Error('文件对象为空')
      }

      // 验证文件是否有name属性
      if (!file.name) {
        throw new Error('文件缺少name属性')
      }

      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch(UploadServer, {
        method: 'POST',
        body: formData,
        headers: {
          // fetch 自动设置 multipart/form-data，无需显式指定
          Authorization: `Bearer ${token}`, // 如果需要认证
        },
      })

      if (!response.ok) {
        throw new Error(`上传失败: ${response.statusText}`)
      }

      const data = await response.json()
      const { full_url } = data.data || {}
      emit('on-upload-success', response)

      const url = full_url

      if (/video/i.test(file.type)) {
        callback(url, {
          name: '视频',
          isBorder: false, // 是否显示边框，默认false
          isShadow: false, // 是否显示阴影，默认false
          isRadius: false, // 是否显示圆角，默认false
          width: '80%',
        })
      } else if (/image/i.test(file.type)) {
        // 如果上传的是图片
        callback(url, {
          name: '图片',
          isBorder: false, // 是否显示边框，默认false
          isShadow: false, // 是否显示阴影，默认false
          isRadius: false, // 是否显示圆角，默认false
          width: '60%', // 图片的宽度，默认100%，可配置百分比，也可配置像素值
          height: 'auto', // 图片的高度，默认auto
        })
      } else {
        // 如果上传的是文件
        callback(url)
      }
    } catch (error) {
      console.error('上传失败:', error)
      callback(null, error.message || error)
    }
  }

  const initCherry = async () => {
    await nextTick()
    if (!cherryContainer.value) return
    const config = {
      el: cherryContainer.value,
      value: props.value || '',
      editor: {
        defaultModel: props.readonly ? 'previewOnly' : 'edit&preview',
      },
      isPreviewOnly: props.readonly,
      header: {
        /**
         * 标题的样式：
         *  - default       默认样式，标题前面有锚点
         *  - autonumber    标题前面有自增序号锚点
         *  - none          标题没有锚点
         */
        anchorStyle: 'none',

        strict: false, // 语法校验是否严格模式
      },
      /**
       * 配置图片懒加载的逻辑
       * - 如果不希望图片懒加载，可配置成 lazyLoadImg = {noLoadImgNum: -1}
       * - 如果希望所有图片都无脑懒加载，可配置成 lazyLoadImg = {noLoadImgNum: 0, autoLoadImgNum: -1}
       * - 如果一共有15张图片，希望：
       *    1、前5张图片（1~5）直接加载；
       *    2、后5张图片（6~10）不论在不在视区内，都无脑懒加载；
       *    3、其他图片（11~15）在视区内时，进行懒加载；
       *    则配置应该为：lazyLoadImg = {noLoadImgNum: 5, autoLoadImgNum: 5}
       */
      lazyLoadImg: {
        // 加载图片时如果需要展示loading图，则配置loading图的地址
        loadingImgPath: '',
        // 同一时间最多有几个图片请求，最大同时加载6张图片
        maxNumPerTime: 1,
        // 不进行懒加载处理的图片数量，如果为0，即所有图片都进行懒加载处理， 如果设置为-1，则所有图片都不进行懒加载处理
        noLoadImgNum: 1,
        // 首次自动加载几张图片（不论图片是否滚动到视野内），autoLoadImgNum = -1 表示会自动加载完所有图片
        autoLoadImgNum: 5,
        // 针对加载失败的图片 或 beforeLoadOneImgCallback 返回false 的图片，最多尝试加载几次，为了防止死循环，最多5次。以图片的src为纬度统计重试次数
        maxTryTimesPerSrc: 2,
        // 加载一张图片之前的回调函数，函数return false 会终止加载操作
        beforeLoadOneImgCallback: (img) => {
          return true
        },
        // 加载一张图片失败之后的回调函数
        failLoadOneImgCallback: (img) => {},
        // 加载一张图片之后的回调函数，如果图片加载失败，则不会回调该函数
        afterLoadOneImgCallback: (img) => {},
        // 加载完所有图片后调用的回调函数
        afterLoadAllImgCallback: () => {},
      },
      toolbars: cherryToolbar,

      callback: {
        /*  内容变化回调 */
        afterChange: async (md, html) => {
          updateOutline()
          emit('content-on-change', getMarkdown())
        },

        /*  图片上传回调 */
        fileUpload: myFileUpload,
      },
      // 目前应用的主题
      mainTheme: 'default',
      // 目前应用的代码块主题
      codeBlockTheme: 'vs dark',
      ...props.options,
    }
    const inst = new Cherry(config)
    cherryInstance.value = markRaw(inst)

    if (!props.readonly) {
      inst.on?.('change', () => {
        emit('update:value', inst.getMarkdown())
      })
    }

    nextTick(() => {
      const el = cherryContainer.value.querySelector('.cherry-previewer')
      if (el) {
        previewerEl.value = el

        const headerDiv = document.createElement('div')
        headerDiv.className = 'firefly-cherry-editor__preview-header-anchor'
        el.insertBefore(headerDiv, el.firstChild)
        previewHeaderEl.value = headerDiv

        updateOutline()
        bindOutlineObserver()
      }
    })
  }

  watch(
    () => props.readonly,
    (v) => {
      if (!cherryInstance.value) return
      v
        ? cherryInstance.value.switchModel('previewOnly')
        : cherryInstance.value.switchModel('edit&preview')

      if (v) {
        ensurePreviewHeader()
        updateOutline()
      }
    }
  )

  watch(
    () => props.value,
    (nv) => {
      if (!cherryInstance.value) return
      if (props.readonly) {
        setValueNotes(nv)
        cosole.log('')
        cherryInstance.value.previewer.scrollToTop(0, 'instant') // 预览区域回到顶端
      }
    }
  )

  onMounted(() => {
    initCherry()
  })
  onUnmounted(() => {
    cherryInstance.value?.destroy?.()
    if (observerInstance) observerInstance.disconnect()
  })

  const getMarkdown = () => cherryInstance.value?.getMarkdown() || ''
  const getHtml = () => cherryInstance.value?.getHtml() || ''

  const getTextValue = () => {
    return getMarkdown()
  }

  // 获取编辑器Markdown值
  const getMarkdownValue = () => {
    return getMarkdown()
  }

  // 获取编辑器HTML值
  const getHtmlValue = () => {
    return getHtml() || ''
  }

  // 获取编辑器值根据传入的标志
  const getValueByType = (isGetHtml = true) => {
    return isGetHtml ? getHtmlValue() : getMarkdownValue()
  }

  // ==== 适配SPA：将相对哈希链接(#xxx)转换为保留当前路由上下文的绝对地址 ====
  const getCurrentSpaRouteUrl = () => {
    try {
      const { origin, pathname, hash } = window.location
      // 直接使用当前地址的“基础 + 路由哈希”，用于前缀化内部章节链接
      // 例如：http://host/#/Oa/Wiki/Detail?space_id=53&doc_id=928
      return `${origin}${pathname}${hash}`
    } catch (e) {
      return ''
    }
  }

  const rewriteRelativeHashLinksForSpa = (raw) => {
    // 仅在只读模式下并且存在window时处理
    if (!props.readonly || typeof window === 'undefined') return raw
    const base = getCurrentSpaRouteUrl()
    if (!base || !raw) return raw

    let content = String(raw)

    // 1) 处理 Markdown 形式的相对哈希链接: [文本](#hash)
    //    转换为: [文本](http://host/#/route...#hash)
    content = content.replace(
      /\[([^\]]+)\]\(#([^)\s]+)\)/g,
      (_m, text, frag) => {
        // 仅处理纯“#片段”链接，已是绝对的或以/开头的链接不处理
        if (!frag) return _m
        // 避免把 http(s) 等协议链接误处理
        if (/^(https?:)?\/\//i.test(frag) || frag.startsWith('/')) return _m
        return `[${text}](${base}#${frag})`
      }
    )

    // 2) 处理 HTML 形式的相对哈希链接: <a href="#hash"> 或 <a href='#hash'>
    content = content
      .replace(/href="#([^"]+)"/g, (_m, frag) => `href="${base}#${frag}"`)
      .replace(/href='#([^']+)'/g, (_m, frag) => `href='${base}#${frag}'`)

    return content
  }

  // 设置编辑器内容
  const setValueNotes = _.debounce((content) => {
    if (!cherryInstance.value) return
    // 在只读渲染路径适配相对哈希链接，确保复制到外部时保留SPA路由上下文
    const adapted = rewriteRelativeHashLinksForSpa(content)
    cherryInstance.value.setMarkdown(adapted, false)
  }, 200)

  defineExpose({
    getMarkdown,
    getHtml,
    setValueNotes,
    getHtmlValue,
    getMarkdownValue,
    getValueByType,
    getTextValue,
  })
</script>

<style scoped lang="scss">
  .firefly-cherry-editor-container {
    position: relative;
    width: 100%;
    height: 100%;

    .editor-main {
      display: flex;
      height: 100%;
    }

    .cherry-markdown-outline {
      border-left: 1px solid #dcdfe6;
      padding: 10px;
      overflow-y: auto;
      font-size: 14px;
      margin-left: 0px;
    }
  }

  .content-header-wrapper {
    position: sticky;
    top: 0;
    background: white;
    z-index: 10;
    width: calc(100% + 40px);
    margin-left: -20px;
  }

  .theme__dark .content-header-wrapper {
    background: #2d2d2d;
  }
</style>

<style lang="scss" scoped>
  :deep() {
    .el-scrollbar__wrap {
      margin-top: 0px !important;
    }

    .cherry {
      box-shadow: none;
    }

    .cherry-previewer {
      border-left: 0px;
      padding-right: 20px;
      padding-top: 0px;
      // margin-right: 10px; // 将导致分屏预览右侧位置异常

      .anchor {
        display: none;
      }
    }

    .cherry-toolbar {
      border-bottom: 1ps solid #dcdfe6;
      box-shadow: 0 0 1px;
    }
    .cherry
      .cherry-previewer-table-content-handler
      .cherry-previewer-table-content-handler__input
      textarea {
      outline: 1px solid #3582fb !important;
    }
    .cherry-previewer-codeBlock-hover-handler .cherry-code-preview-lang-select {
      transform: translate(2px, -70%);
    }
  }
</style>

<style lang="scss">
  .cherry-markdown-outline {
    width: 16vw;
    max-height: calc(100vh - 186px);
    padding: 0 8px;
    margin-left: 20px;
    overflow: auto;
    border-left: 1px solid #dcdfe6;
    font-family: 'HuaweiSans', 'PingFang SC', Arial, 'Microsoft YaHei',
      sans-serif;

    /* 可选标题，如果需要 */
    // &::before {
    //   content: "目录";
    //   display: block;
    //   padding-bottom: 8px;
    //   margin-bottom: 12px;
    //   font-size: 16px;
    //   font-weight: 600;
    //   color: #333;
    //   border-bottom: 1px solid #e9e9e9;
    // }

    .cherry-markdown-outline__item {
      margin: 0;
      padding: 0;

      a {
        display: inline-block;
        width: 100%;
        height: 30px;
        line-height: 30px;
        padding-left: 4px; // 基础缩进
        font-size: 14px;
        color: #666 !important;
        text-decoration: none;
        text-indent: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-radius: 4px;
        transition: background-color 0.3s ease;

        &:hover {
          cursor: pointer;
          background-color: #334d660f;
        }
      }
    }
  }

  /* 空大纲提示 */
  .outline-empty {
    padding: 10px;
    margin-top: 20px;
    font-style: italic;
    color: #999;
    text-align: center;
  }
</style>
